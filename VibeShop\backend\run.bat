@echo off
echo Starting VibeShop backend server...

:: Check if virtual environment exists
if not exist venv (
    echo Virtual environment not found. Please run setup.bat first.
    pause
    exit /b 1
)

:: Activate virtual environment
call venv\Scripts\activate.bat

:: Display IP address information
echo.
echo Your current IP address(es):
ipconfig | findstr /i "IPv4"
echo.
echo Make sure to update the BASE_URL in src/config/api.js with your IP address
echo Example: export const BASE_URL = 'http://***********:5000';
echo.

:: Run the server
echo Starting server...
python run.py

:: If server exits, deactivate virtual environment
call venv\Scripts\deactivate.bat

pause 