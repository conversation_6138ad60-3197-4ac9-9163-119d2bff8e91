import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
} from "react-native";
import VoiceButton from "../components/VoiceButton";
import VoiceTranscriptWrapper from "../components/VoiceTranscriptWrapper";
import cartService from "../services/cartService";
import paymentService from "../services/paymentService";
import voiceService from "../services/voiceService";
import { useVoice } from "../context/VoiceContext";

const CheckoutScreen = ({ route, navigation }) => {
  const { cart } = route.params || {};
  const [userInfo, setUserInfo] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
  });
  const [loading, setLoading] = useState(false);
  const { lastCommand } = useVoice();

  // Handle voice commands
  useEffect(() => {
    if (lastCommand) {
      const commandLower = lastCommand.toLowerCase();

      // Handle confirm payment command with common speech recognition errors
      // Only process if the form is valid
      if (
        (commandLower.includes("confirm") ||
          commandLower.includes("pay now") ||
          commandLower.includes("complete payment") ||
          commandLower.includes("process payment") ||
          commandLower.includes("make payment") ||
          commandLower.includes("finish")) &&
        isFormValid()
      ) {
        handlePayment();
      }

      // Handle back command
      if (
        commandLower.includes("go back") ||
        commandLower.includes("back") ||
        commandLower.includes("return")
      ) {
        handleBackPress();
      }

      // Handle cancel command
      if (
        commandLower.includes("cancel") ||
        commandLower.includes("abort") ||
        commandLower.includes("stop")
      ) {
        navigation.navigate("Cart");
      }

      // If user tries to confirm payment but form is not valid, provide feedback
      if (
        (commandLower.includes("confirm") ||
          commandLower.includes("pay now") ||
          commandLower.includes("complete payment") ||
          commandLower.includes("process payment") ||
          commandLower.includes("make payment") ||
          commandLower.includes("finish")) &&
        !isFormValid()
      ) {
        Alert.alert(
          "Form Incomplete",
          "Please fill in all required fields before confirming payment.",
          [{ text: "OK" }]
        );
      }
    }
  }, [lastCommand, navigation, isFormValid, handlePayment, handleBackPress]);

  // Handle input change
  const handleInputChange = (field, value) => {
    setUserInfo({
      ...userInfo,
      [field]: value,
    });
  };

  // Check if form is valid
  const isFormValid = () => {
    return (
      userInfo.name.trim() !== "" &&
      userInfo.email.trim() !== "" &&
      userInfo.phone.trim() !== "" &&
      userInfo.address.trim() !== ""
    );
  };

  // Handle payment
  const handlePayment = async () => {
    try {
      if (!isFormValid()) {
        Alert.alert(
          "Missing Information",
          "Please fill in all required fields."
        );

        // Provide voice feedback if available
        if (voiceService && voiceService.speak) {
          try {
            await voiceService.speak(
              "Please fill in all required fields before confirming payment."
            );
          } catch (error) {
            console.error("Error speaking feedback:", error);
          }
        }

        return;
      }

      setLoading(true);

      // Create order in Firestore
      const order = await cartService.createOrder(userInfo);

      // Initialize payment with Paystack
      const paymentInfo = paymentService.initializePayment(
        order,
        userInfo.email
      );

      // Navigate to payment screen
      navigation.navigate("Payment", {
        paymentInfo,
        order,
        userInfo,
      });
    } catch (error) {
      console.error("Error processing checkout:", error);
      Alert.alert(
        "Checkout Error",
        "Failed to process checkout. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle back navigation
  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <VoiceTranscriptWrapper position="bottom">
      <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor='#6200ee' barStyle='light-content' />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerText}>Checkout</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Order Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Items:</Text>
            <Text style={styles.summaryValue}>{cart?.totalItems || 0}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total:</Text>
            <Text style={styles.summaryTotal}>
              GHS {(cart?.totalAmount || 0).toFixed(2)}
            </Text>
          </View>
        </View>

        {/* Customer Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Customer Information</Text>

          <Text style={styles.inputLabel}>Full Name *</Text>
          <TextInput
            style={styles.input}
            placeholder='Enter your full name'
            value={userInfo.name}
            onChangeText={(text) => handleInputChange("name", text)}
          />

          <Text style={styles.inputLabel}>Email *</Text>
          <TextInput
            style={styles.input}
            placeholder='Enter your email'
            keyboardType='email-address'
            value={userInfo.email}
            onChangeText={(text) => handleInputChange("email", text)}
          />

          <Text style={styles.inputLabel}>Phone Number *</Text>
          <TextInput
            style={styles.input}
            placeholder='Enter your phone number'
            keyboardType='phone-pad'
            value={userInfo.phone}
            onChangeText={(text) => handleInputChange("phone", text)}
          />

          <Text style={styles.inputLabel}>Delivery Address *</Text>
          <TextInput
            style={[styles.input, styles.addressInput]}
            placeholder='Enter your delivery address'
            multiline
            numberOfLines={3}
            value={userInfo.address}
            onChangeText={(text) => handleInputChange("address", text)}
          />
        </View>

        {/* Payment Method */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          <Text style={styles.paymentInfo}>
            Payment will be processed securely via Paystack Ghana
          </Text>

          <View style={styles.paymentMethods}>
            <Text style={styles.paymentMethodTitle}>
              Available Payment Options:
            </Text>
            <Text style={styles.paymentMethod}>
              • Mobile Money (MTN, Vodafone, AirtelTigo)
            </Text>
            <Text style={styles.paymentMethod}>• Credit/Debit Cards</Text>
            <Text style={styles.paymentMethod}>• Bank Transfer</Text>
          </View>
        </View>

        {/* Voice Instructions */}
        <View style={styles.voiceInstructionsContainer}>
          <Text style={styles.voiceInstructionsTitle}>Voice Commands</Text>
          <Text style={styles.voiceInstructionsText}>
            Say "Confirm payment" to proceed with payment
          </Text>
          <Text style={styles.voiceInstructionsText}>
            Say "Go back" to return to cart
          </Text>
          <Text style={styles.voiceInstructionsText}>
            Say "Cancel" to cancel checkout
          </Text>
        </View>

        {/* Payment Button */}
        <TouchableOpacity
          style={[
            styles.paymentButton,
            !isFormValid() && styles.paymentButtonDisabled,
          ]}
          onPress={handlePayment}
          disabled={!isFormValid() || loading}
        >
          <Text style={styles.paymentButtonText}>
            {loading ? "Processing..." : "Confirm Payment"}
          </Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Voice Button */}
      <View style={styles.floatingButtonContainer}>
        <VoiceButton minimized={true} />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f8f8",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#6200ee",
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
  },
  headerText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 80,
  },
  section: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#333",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: "#666",
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  summaryTotal: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#6200ee",
  },
  inputLabel: {
    fontSize: 14,
    color: "#333",
    marginBottom: 4,
  },
  input: {
    backgroundColor: "#f5f5f5",
    borderRadius: 5,
    padding: 10,
    marginBottom: 16,
    fontSize: 16,
  },
  addressInput: {
    height: 80,
    textAlignVertical: "top",
  },
  paymentInfo: {
    fontSize: 14,
    color: "#666",
    marginBottom: 16,
  },
  paymentMethods: {
    backgroundColor: "#f5f5f5",
    padding: 12,
    borderRadius: 5,
  },
  paymentMethodTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#333",
  },
  paymentMethod: {
    fontSize: 14,
    color: "#333",
    marginBottom: 4,
  },
  voiceInstructionsContainer: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    alignItems: "center",
  },
  voiceInstructionsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#6200ee",
  },
  voiceInstructionsText: {
    fontSize: 14,
    color: "#333",
    marginBottom: 4,
  },
  paymentButton: {
    backgroundColor: "#6200ee",
    paddingVertical: 16,
    borderRadius: 5,
    alignItems: "center",
    marginTop: 8,
  },
  paymentButtonDisabled: {
    backgroundColor: "#b39ddb",
  },
  paymentButtonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  floatingButtonContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
    zIndex: 999,
  },
});

export default CheckoutScreen;
