import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import ProductCard from "../components/ProductCard";
import VoiceButton from "../components/VoiceButton";
import VoiceTranscriptWrapper from "../components/VoiceTranscriptWrapper";
import { useVoice } from "../context/VoiceContext";
import productService from "../services/productService";

const SearchResultsScreen = ({ route, navigation }) => {
  const { searchTerm = "", products = [] } = route.params || {};
  const [searchResults, setSearchResults] = useState(products);
  const [loading, setLoading] = useState(false);
  const { lastCommand, navigate } = useVoice();

  // Use the navigate function from VoiceContext if available, otherwise use the navigation prop
  const navigateTo = (screen, params) => {
    if (navigate) {
      navigate(screen, params);
    } else if (navigation) {
      navigation.navigate(screen, params);
    }
  };

  // Handle voice commands
  useEffect(() => {
    if (lastCommand) {
      const commandLower = lastCommand.toLowerCase();

      // Handle new search command
      if (commandLower.includes("search")) {
        const newSearchTerm = commandLower
          .replace("search for", "")
          .replace("search", "")
          .trim();
        if (newSearchTerm && newSearchTerm !== searchTerm) {
          handleSearch(newSearchTerm);
        }
      }

      // Handle selection commands
      if (commandLower.includes("select")) {
        // Extract number from command (e.g., "select item one" or "select first item")
        let itemIndex = -1;

        if (commandLower.includes("first") || commandLower.includes("one")) {
          itemIndex = 0;
        } else if (
          commandLower.includes("second") ||
          commandLower.includes("two")
        ) {
          itemIndex = 1;
        } else if (
          commandLower.includes("third") ||
          commandLower.includes("three")
        ) {
          itemIndex = 2;
        } else {
          // Try to extract a numeric value
          const match = commandLower.match(/\b(\d+)\b/);
          if (match) {
            itemIndex = parseInt(match[1], 10) - 1; // Convert to 0-based index
          }
        }

        // Select the product if valid index
        if (itemIndex >= 0 && itemIndex < searchResults.length) {
          handleProductPress(searchResults[itemIndex]);
        }
      }

      // Handle back command
      if (commandLower.includes("go back") || commandLower === "back") {
        navigation.goBack();
      }
    }
  }, [lastCommand, searchTerm, searchResults]);

  // Handle search
  const handleSearch = async (term) => {
    try {
      setLoading(true);
      const results = await productService.getMockProducts(term);
      setSearchResults(results);
      setLoading(false);

      // Update navigation params
      navigation.setParams({ searchTerm: term, products: results });
    } catch (error) {
      console.error("Error searching products:", error);
      setLoading(false);
    }
  };

  // Handle product selection
  const handleProductPress = (product) => {
    navigateTo("ProductDetail", {
      productId: product.id,
      productName: product.name,
      product,
    });
  };

  // Handle back button press
  const handleBackPress = () => {
    navigation.goBack();
  };

  // Render header with search term
  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
        <Text style={styles.backButtonText}>←</Text>
      </TouchableOpacity>
      <View style={styles.headerTitleContainer}>
        <Text style={styles.headerTitle}>Search Results</Text>
        <Text style={styles.searchTerm}>"{searchTerm}"</Text>
      </View>
    </View>
  );

  // Render product list
  const renderProductList = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color='#6200ee' />
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      );
    }

    if (searchResults.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            No products found for "{searchTerm}"
          </Text>
          <Text style={styles.emptySubText}>
            Try searching for something else
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={searchResults}
        renderItem={({ item, index }) => (
          <ProductCard
            product={item}
            onPress={handleProductPress}
            index={index + 1}
          />
        )}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.productList}
      />
    );
  };

  // Render voice instructions
  const renderVoiceInstructions = () => (
    <View style={styles.voiceInstructionsContainer}>
      <Text style={styles.voiceInstructionsTitle}>Voice Commands</Text>
      <View style={styles.commandsGrid}>
        <View style={styles.commandItem}>
          <Text style={styles.commandIcon}>👆</Text>
          <Text style={styles.voiceInstructionsText}>"Select item one"</Text>
        </View>
        <View style={styles.commandItem}>
          <Text style={styles.commandIcon}>🔍</Text>
          <Text style={styles.voiceInstructionsText}>"Search for [item]"</Text>
        </View>
        <View style={styles.commandItem}>
          <Text style={styles.commandIcon}>↩️</Text>
          <Text style={styles.voiceInstructionsText}>"Go back"</Text>
        </View>
      </View>
    </View>
  );

  return (
    <VoiceTranscriptWrapper position='bottom'>
      <SafeAreaView style={styles.container}>
        {renderHeader()}

        {renderVoiceInstructions()}

        {renderProductList()}

        <View style={styles.voiceButtonContainer}>
          <VoiceButton />
        </View>
      </SafeAreaView>
    </VoiceTranscriptWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    backgroundColor: "#6200ee",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  backButtonText: {
    fontSize: 24,
    color: "#fff",
  },
  headerTitleContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
  },
  searchTerm: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    marginTop: 2,
  },
  voiceInstructionsContainer: {
    margin: 15,
    padding: 15,
    backgroundColor: "#f5f5f5",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  voiceInstructionsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#333",
  },
  commandsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: "wrap",
  },
  commandItem: {
    flexDirection: "row",
    alignItems: "center",
    width: "30%",
    marginBottom: 10,
  },
  commandIcon: {
    fontSize: 18,
    marginRight: 5,
  },
  voiceInstructionsText: {
    fontSize: 14,
    color: "#555",
    flex: 1,
  },
  productList: {
    padding: 15,
    paddingBottom: 100, // Extra padding at bottom for voice button
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    color: "#666",
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: "#333",
    textAlign: "center",
    marginBottom: 10,
  },
  emptySubText: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
  voiceButtonContainer: {
    position: "absolute",
    bottom: 20,
    alignSelf: "center",
  },
});

export default SearchResultsScreen;
