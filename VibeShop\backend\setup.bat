@echo off
echo Setting up VibeShop backend environment...

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python is not installed or not in PATH. Please install Python 3.8 or later.
    exit /b 1
)

:: Check if pip is installed
pip --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo pip is not installed or not in PATH. Please install pip.
    exit /b 1
)

:: Create virtual environment if it doesn't exist
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
) else (
    echo Virtual environment already exists.
)

:: Activate virtual environment and install requirements
echo Activating virtual environment and installing dependencies...
call venv\Scripts\activate.bat

:: Install required packages
echo Installing required packages...
pip install -r requirements.txt

:: Check if installation was successful
if %ERRORLEVEL% NEQ 0 (
    echo Failed to install dependencies. Please check your internet connection and try again.
    exit /b 1
)

echo.
echo Setup complete! The backend environment is ready.
echo.
echo To start the backend server:
echo 1. Run 'run.bat' or
echo 2. Activate the virtual environment with 'venv\Scripts\activate.bat' and run 'python run.py'
echo.

pause 