import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useVoice } from '../context/VoiceContext';
import VoiceTranscriptOverlay from './VoiceTranscriptOverlay';

const VoiceTranscriptWrapper = ({ children, position = 'bottom' }) => {
  const { showTranscript, currentTranscript, closeTranscript } = useVoice();

  return (
    <View style={styles.container}>
      {children}
      <VoiceTranscriptOverlay
        transcript={currentTranscript}
        isVisible={showTranscript}
        onClose={closeTranscript}
        position={position}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default VoiceTranscriptWrapper;
