import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator,
} from "react-native";
import { usePaystack } from "react-native-paystack-webview";
import paymentService from "../services/paymentService";
import { useVoice } from "../context/VoiceContext";
import VoiceTranscriptWrapper from "../components/VoiceTranscriptWrapper";

const PaymentScreen = ({ route, navigation }) => {
  const { paymentInfo, order, userInfo } = route.params || {};
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const { speakFeedback, lastCommand } = useVoice();
  const { popup } = usePaystack();

  useEffect(() => {
    // Auto-initialize payment when screen loads
    if (paymentInfo) {
      setTimeout(() => {
        setLoading(false);
        // Don't auto-initiate payment to give user time to read summary
      }, 1500);
    } else {
      // No payment info provided, go back
      Alert.alert("Error", "No payment information provided");
      navigation.goBack();
    }
  }, [paymentInfo]);

  // Handle voice commands
  useEffect(() => {
    if (lastCommand) {
      const commandLower = lastCommand.toLowerCase();

      // Handle payment commands - but require actual payment process
      if (
        commandLower.includes("pay now") ||
        commandLower.includes("make payment") ||
        commandLower.includes("process payment") ||
        commandLower.includes("complete payment")
      ) {
        speakFeedback(
          "Please use the Pay with Paystack button to complete your payment securely."
        );
      }

      // Handle back command
      if (
        commandLower.includes("go back") ||
        commandLower.includes("back") ||
        commandLower.includes("return")
      ) {
        navigation.goBack();
      }

      // Handle cancel command
      if (commandLower.includes("cancel") || commandLower.includes("abort")) {
        navigation.navigate("Cart");
      }
    }
  }, [lastCommand, navigation, speakFeedback]);

  // Initiate payment using Paystack popup
  const initiatePayment = () => {
    if (!paymentInfo) return;

    try {
      setProcessing(true);
      popup({
        publicKey: paymentInfo.publicKey,
        email: paymentInfo.paymentData.email,
        amount: paymentInfo.paymentData.amount / 100, // Divide by 100 since Paystack will multiply by 100 again
        currency: "GHS",
        reference: paymentInfo.paymentData.reference,
        onSuccess: handlePaymentSuccess,
        onCancel: handlePaymentCancel,
        onError: handlePaymentError,
      });
    } catch (error) {
      console.error("Error initiating payment:", error);
      setProcessing(false);
      Alert.alert("Error", "Failed to initialize payment gateway");
    }
  };

  // Handle payment response
  const handlePaymentSuccess = async (response) => {
    try {
      setProcessing(true);
      const reference =
        response?.data?.transactionRef?.reference ||
        paymentInfo?.paymentData?.reference;

      // Verify payment with Paystack
      const verificationResult = await paymentService.verifyPayment(reference);

      if (verificationResult && verificationResult.status) {
        // Update order status in Firestore
        await paymentService.updateOrderAfterPayment(
          order.orderId,
          verificationResult.data
        );

        // Provide voice feedback
        speakFeedback("Payment successful! Your order has been placed.");

        // Navigate to success screen
        navigation.navigate("OrderSuccess", {
          order,
          paymentDetails: verificationResult.data,
        });
      } else {
        // Payment verification failed
        Alert.alert(
          "Payment Failed",
          "Your payment could not be verified. Please try again."
        );
      }
    } catch (error) {
      console.error("Payment verification error:", error);
      Alert.alert(
        "Verification Error",
        "There was an error verifying your payment."
      );
    } finally {
      setProcessing(false);
    }
  };

  const handlePaymentCancel = () => {
    setProcessing(false);
    Alert.alert(
      "Payment Cancelled",
      "Your payment was cancelled. Would you like to try again?",
      [
        { text: "No", onPress: () => navigation.goBack() },
        {
          text: "Yes",
          onPress: initiatePayment,
        },
      ]
    );
  };

  const handlePaymentError = (error) => {
    console.error("Payment error:", error);
    setProcessing(false);
    Alert.alert(
      "Payment Failed",
      "There was an error processing your payment. Please try again.",
      [
        { text: "Cancel", onPress: () => navigation.goBack() },
        {
          text: "Try Again",
          onPress: initiatePayment,
        },
      ]
    );
  };

  const handleBackPress = () => {
    if (processing) {
      Alert.alert(
        "Payment in Progress",
        "Please wait while your payment is being processed."
      );
      return;
    }

    Alert.alert(
      "Cancel Payment",
      "Are you sure you want to cancel this payment?",
      [
        {
          text: "Continue Payment",
          style: "cancel",
        },
        {
          text: "Cancel Payment",
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  // For demo purposes, we'll provide a simulated payment button
  const handleSimulatePayment = async () => {
    try {
      setProcessing(true);

      // Simulate payment processing delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Create mock verification result
      const mockVerificationResult = {
        status: true,
        message: "Verification successful",
        data: {
          status: "success",
          reference: paymentInfo?.paymentData?.reference || `REF-${Date.now()}`,
          amount: paymentInfo?.paymentData?.amount || 10000,
          paid_at: new Date().toISOString(),
        },
      };

      // Update order status in Firestore
      await paymentService.updateOrderAfterPayment(
        order.orderId,
        mockVerificationResult.data
      );

      // Provide voice feedback
      speakFeedback("Payment successful! Your order has been placed.");

      // Navigate to success screen
      navigation.navigate("OrderSuccess", {
        order,
        paymentDetails: mockVerificationResult.data,
      });
    } catch (error) {
      console.error("Error simulating payment:", error);
      Alert.alert("Error", "There was an error processing your payment.");
    } finally {
      setProcessing(false);
    }
  };

  return (
    <VoiceTranscriptWrapper position='bottom'>
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor='#6200ee' barStyle='light-content' />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.headerText}>Payment</Text>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size='large' color='#6200ee' />
            <Text style={styles.loadingText}>Loading payment gateway...</Text>
          </View>
        ) : processing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size='large' color='#6200ee' />
            <Text style={styles.loadingText}>Processing your payment...</Text>
            <Text style={styles.processingText}>
              Please do not close the app
            </Text>
          </View>
        ) : (
          <View style={styles.contentContainer}>
            {/* Payment Summary */}
            <View style={styles.summaryContainer}>
              <Text style={styles.summaryTitle}>Payment Summary</Text>

              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Amount:</Text>
                <Text style={styles.summaryValue}>
                  GHS{" "}
                  {((paymentInfo?.paymentData?.amount || 0) / 100).toFixed(2)}
                </Text>
              </View>

              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Reference:</Text>
                <Text style={styles.summaryValue}>
                  {paymentInfo?.paymentData?.reference || "N/A"}
                </Text>
              </View>
            </View>

            {/* Payment Buttons */}
            <TouchableOpacity
              style={styles.payButton}
              onPress={initiatePayment}
            >
              <Text style={styles.payButtonText}>Pay with Paystack</Text>
            </TouchableOpacity>

            <Text style={styles.orText}>OR</Text>

            {/* Simulated Payment Options - For Demo Purposes */}
            <View style={styles.paymentOptionsContainer}>
              <Text style={styles.paymentOptionsTitle}>
                Simulate Payment (Demo Only)
              </Text>

              <TouchableOpacity
                style={styles.paymentOption}
                onPress={handleSimulatePayment}
              >
                <Text style={styles.paymentOptionText}>
                  Simulate Successful Payment
                </Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.disclaimerText}>
              This app uses Paystack test mode. No real charges will be made.
              Use test card: 4084 0840 8408 4081, CVV: 408, Any future date,
              PIN: 0000, OTP: 123456
            </Text>
          </View>
        )}
      </SafeAreaView>
    </VoiceTranscriptWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f8f8",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#6200ee",
    padding: 16,
    elevation: 4,
  },
  backButton: {
    marginRight: 16,
  },
  backButtonText: {
    fontSize: 16,
    color: "#fff",
  },
  headerText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: "#333",
    marginTop: 16,
  },
  processingText: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
    fontStyle: "italic",
  },
  contentContainer: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#333",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: "#666",
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  payButton: {
    backgroundColor: "#6200ee",
    borderRadius: 5,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  payButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  orText: {
    textAlign: "center",
    fontSize: 16,
    fontWeight: "bold",
    color: "#666",
    marginVertical: 16,
  },
  paymentOptionsContainer: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  paymentOptionsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#333",
  },
  paymentOption: {
    backgroundColor: "#f5f5f5",
    borderRadius: 5,
    padding: 16,
    marginBottom: 8,
  },
  paymentOptionText: {
    fontSize: 16,
    color: "#333",
  },
  disclaimerText: {
    fontSize: 12,
    color: "#666",
    fontStyle: "italic",
    textAlign: "center",
    marginTop: 16,
  },
});

export default PaymentScreen;
