/**
 * Utility functions for processing voice commands
 */

// Command categories for better organization
const COMMAND_CATEGORIES = {
  SEARCH: "search",
  CART: "cart",
  CHECKOUT: "checkout",
  PAYMENT: "payment",
  NAVIGATION: "navigation",
  PRODUCT: "product",
  HELP: "help",
  CANCEL: "cancel",
};

// Command patterns for each category
const COMMAND_PATTERNS = {
  [COMMAND_CATEGORIES.SEARCH]: [
    {
      regex: /\b(?:search|find|look|show)\s+(?:for|me)?\s+(.+)/i,
      action: "search",
    },
    { regex: /\bfind\s+(.+)/i, action: "search" },
    { regex: /\bshow\s+(?:me)?\s+(.+)/i, action: "search" },
    { regex: /\bi(?:'m| am) looking for\s+(.+)/i, action: "search" },
    { regex: /\bi need\s+(.+)/i, action: "search" },
    { regex: /\bi want\s+(.+)/i, action: "search" },
  ],
  [COMMAND_CATEGORIES.CART]: [
    {
      regex: /\badd\s+(?:this|it|that|the .+)?\s+to\s+(?:my)?\s*cart/i,
      action: "add_to_cart",
    },
    {
      regex: /\badd\s+(?:this|it|that|the .+)?\s+to\s+(?:my)?\s*cut/i,
      action: "add_to_cart",
    }, // Common speech recognition error
    {
      regex: /\badd\s+(?:this|it|that|the .+)?\s+to\s+(?:my)?\s*car/i,
      action: "add_to_cart",
    }, // Common speech recognition error
    {
      regex: /\bput\s+(?:this|it|that|the .+)?\s+in\s+(?:my)?\s*cart/i,
      action: "add_to_cart",
    },
    { regex: /\bbuy\s+(?:this|it|that|the .+)?/i, action: "add_to_cart" },
    {
      regex: /\b(?:view|show|open|go to|see)\s+(?:my)?\s*cart/i,
      action: "view_cart",
    },
    {
      regex: /\b(?:view|show|open|go to|see)\s+(?:my)?\s*card/i,
      action: "view_cart",
    }, // Common speech recognition error
    {
      regex: /\b(?:view|show|open|go to|see)\s+(?:my)?\s*court/i,
      action: "view_cart",
    }, // Common speech recognition error

    // Item-specific removal commands
    {
      regex: /\bremove\s+(.+?)\s+from\s+(?:my)?\s*cart/i,
      action: "remove_item_by_name",
    },
    {
      regex: /\bdelete\s+(.+?)\s+from\s+(?:my)?\s*cart/i,
      action: "remove_item_by_name",
    },
    {
      regex: /\btake\s+(?:out|away)\s+(.+?)\s+from\s+(?:my)?\s*cart/i,
      action: "remove_item_by_name",
    },

    // Generic removal (for current selected item)
    {
      regex: /\bremove\s+(?:this|it|that)\s+from\s+(?:my)?\s*cart/i,
      action: "remove_from_cart",
    },
    {
      regex: /\bdelete\s+(?:this|it|that)\s+from\s+(?:my)?\s*cart/i,
      action: "remove_from_cart",
    },

    // Quantity increase commands
    {
      regex: /\bincrease\s+(?:the\s+)?quantity\s+(?:of\s+)?(.+)/i,
      action: "increase_quantity_by_name",
    },
    {
      regex: /\badd\s+(?:more|another)\s+(.+)/i,
      action: "increase_quantity_by_name",
    },
    { regex: /\bincrease\s+(.+)/i, action: "increase_quantity_by_name" },
    { regex: /\bmore\s+(.+)/i, action: "increase_quantity_by_name" },

    // Quantity decrease commands
    {
      regex: /\bdecrease\s+(?:the\s+)?quantity\s+(?:of\s+)?(.+)/i,
      action: "decrease_quantity_by_name",
    },
    {
      regex: /\breduce\s+(?:the\s+)?quantity\s+(?:of\s+)?(.+)/i,
      action: "decrease_quantity_by_name",
    },
    { regex: /\bless\s+(.+)/i, action: "decrease_quantity_by_name" },
    { regex: /\bdecrease\s+(.+)/i, action: "decrease_quantity_by_name" },

    // Generic quantity commands (for current selected item)
    { regex: /\bincrease\s+(?:the\s+)?quantity/i, action: "increase_quantity" },
    { regex: /\bdecrease\s+(?:the\s+)?quantity/i, action: "decrease_quantity" },
    { regex: /\bupdate\s+(?:the)?\s*quantity/i, action: "update_quantity" },

    { regex: /\bclear\s+(?:my)?\s*cart/i, action: "clear_cart" },
  ],
  [COMMAND_CATEGORIES.CHECKOUT]: [
    {
      regex: /\b(?:proceed to|start|begin|go to)\s+check(?:out)?/i,
      action: "checkout",
    },
    {
      regex: /\b(?:proceed to|start|begin|go to)\s+check\s+out/i,
      action: "checkout",
    }, // Space between check out
    { regex: /\bcheck(?:out)?\b/i, action: "checkout" },
    { regex: /\bcheck\s+out\b/i, action: "checkout" }, // Space between check out
    { regex: /\bplace\s+(?:my)?\s*order/i, action: "checkout" },
    { regex: /\bfinish\s+(?:my)?\s*order/i, action: "checkout" },
    { regex: /\bcomplete\s+(?:my)?\s*purchase/i, action: "checkout" },
  ],
  [COMMAND_CATEGORIES.PAYMENT]: [
    {
      regex: /\b(?:confirm|complete|make|process)\s+(?:my)?\s*payment/i,
      action: "confirm_payment",
    },
    { regex: /\bpay\s+(?:now|for this)/i, action: "confirm_payment" },
    { regex: /\bfinalize\s+(?:my)?\s*purchase/i, action: "confirm_payment" },
    { regex: /\bcomplete\s+(?:my)?\s*order/i, action: "confirm_payment" },
  ],
  [COMMAND_CATEGORIES.NAVIGATION]: [
    {
      regex: /\bgo\s+(?:back|home|to home)/i,
      action: "navigate",
      payload: "home",
    },
    {
      regex: /\breturn\s+(?:back|home|to home)/i,
      action: "navigate",
      payload: "home",
    },
    { regex: /\bhome\s*page/i, action: "navigate", payload: "home" },
  ],
  [COMMAND_CATEGORIES.PRODUCT]: [
    {
      regex: /\bselect\s+(?:the)?\s*(\w+)(?:\s+one)?/i,
      action: "select_product",
    },
    {
      regex: /\bchoose\s+(?:the)?\s*(\w+)(?:\s+one)?/i,
      action: "select_product",
    },
    {
      regex: /\bmore\s+(?:details|info|information)/i,
      action: "product_details",
    },
    {
      regex: /\b(?:tell me|show)\s+more\s+about\s+this/i,
      action: "product_details",
    },
  ],
  [COMMAND_CATEGORIES.HELP]: [
    { regex: /\b(?:help|assist)(?:\s+me)?/i, action: "help" },
    { regex: /\bwhat\s+can\s+(?:i|you)\s+(?:say|do)/i, action: "help" },
    { regex: /\bhow\s+(?:does|do)\s+(?:this|it)\s+work/i, action: "help" },
  ],
  [COMMAND_CATEGORIES.CANCEL]: [
    { regex: /\b(?:cancel|stop|nevermind|never mind)/i, action: "cancel" },
    { regex: /\bgo\s+back/i, action: "cancel" },
  ],
};

/**
 * Extract search term from a voice command
 * @param {string} command - The voice command
 * @returns {string|null} - The extracted search term or null if not found
 */
export const extractSearchTerm = (command) => {
  if (!command) return null;

  const commandLower = command.toLowerCase().trim();

  // Try all search patterns
  for (const pattern of COMMAND_PATTERNS[COMMAND_CATEGORIES.SEARCH]) {
    const match = commandLower.match(pattern.regex);
    if (match && match[1]) {
      return match[1].trim();
    }
  }

  // Legacy patterns (kept for backward compatibility)
  if (commandLower.startsWith("search for ")) {
    return commandLower.replace("search for ", "").trim();
  } else if (commandLower.startsWith("search ")) {
    return commandLower.replace("search ", "").trim();
  } else if (commandLower.startsWith("find ")) {
    return commandLower.replace("find ", "").trim();
  } else if (commandLower.startsWith("look for ")) {
    return commandLower.replace("look for ", "").trim();
  }

  return null;
};

/**
 * Extract product selection information
 * @param {string} command - The voice command
 * @returns {string|number|null} - The extracted product identifier or null if not found
 */
export const extractProductSelection = (command) => {
  if (!command) return null;

  const commandLower = command.toLowerCase().trim();

  // Check for numeric selections
  const numericMatch = commandLower.match(
    /\b(?:select|choose|pick|get)\s+(?:the|item|product|number)?\s*(\d+)/i
  );
  if (numericMatch && numericMatch[1]) {
    return parseInt(numericMatch[1], 10);
  }

  // Check for positional selections (first, second, etc.)
  const positionMatch = commandLower.match(
    /\b(?:select|choose|pick|get)\s+(?:the)?\s*(first|second|third|fourth|fifth)/i
  );
  if (positionMatch && positionMatch[1]) {
    const positions = { first: 1, second: 2, third: 3, fourth: 4, fifth: 5 };
    return positions[positionMatch[1]];
  }

  // Check for brand name or product type selections
  const brandMatch = commandLower.match(
    /\b(?:select|choose|pick|get)\s+(?:the)?\s*(\w+)\b/i
  );
  if (brandMatch && brandMatch[1]) {
    // Return the brand name as a string for custom handling
    return brandMatch[1].trim();
  }

  return null;
};

/**
 * Extract quantity information from a command
 * @param {string} command - The voice command
 * @returns {number|null} - The extracted quantity or null if not found
 */
export const extractQuantity = (command) => {
  if (!command) return null;

  const commandLower = command.toLowerCase().trim();

  // Check for quantity patterns
  const quantityMatch = commandLower.match(
    /\b(?:set|change|update|make)\s+(?:the)?\s*quantity\s+(?:to|as)?\s*(\d+)/i
  );
  if (quantityMatch && quantityMatch[1]) {
    return parseInt(quantityMatch[1], 10);
  }

  return null;
};

/**
 * Determine the action type from a voice command
 * @param {string} command - The voice command
 * @returns {Object} - Action type and payload
 */
export const determineAction = (command) => {
  if (!command) return { action: "unknown", payload: null };

  const commandLower = command.toLowerCase().trim();

  // Check for search commands first
  const searchTerm = extractSearchTerm(commandLower);
  if (searchTerm) {
    return { action: "search", payload: searchTerm };
  }

  // Check all other command patterns
  for (const category in COMMAND_PATTERNS) {
    for (const pattern of COMMAND_PATTERNS[category]) {
      const match = commandLower.match(pattern.regex);
      if (match) {
        // For commands with captured groups as payload
        if (match[1] && !pattern.payload) {
          return { action: pattern.action, payload: match[1].trim() };
        }
        // For commands with predefined payload
        return {
          action: pattern.action,
          payload: pattern.payload || null,
        };
      }
    }
  }

  // Product selection commands
  const productSelection = extractProductSelection(commandLower);
  if (productSelection !== null) {
    return { action: "select_product", payload: productSelection };
  }

  // Quantity update commands
  const quantity = extractQuantity(commandLower);
  if (quantity !== null) {
    return { action: "update_quantity", payload: quantity };
  }

  // If no pattern matches, return unknown action
  return { action: "unknown", payload: commandLower };
};

/**
 * Generate a response message for a given action
 * @param {string} action - The action type
 * @param {any} payload - The action payload
 * @returns {string} - Response message
 */
export const generateResponse = (action, payload) => {
  switch (action) {
    case "search":
      return `Searching for "${payload}"`;
    case "add_to_cart":
      return "Adding item to your cart";
    case "remove_from_cart":
      return "Removing item from your cart";
    case "clear_cart":
      return "Clearing your cart";
    case "update_quantity":
      return `Updating quantity to ${payload}`;
    case "view_cart":
      return "Opening your cart";
    case "select_product":
      return `Selecting product ${payload}`;
    case "product_details":
      return "Showing product details";
    case "checkout":
      return "Proceeding to checkout";
    case "confirm_payment":
      return "Confirming your payment";
    case "navigate":
      return `Navigating to ${payload || "previous page"}`;
    case "help":
      return "Here are the available commands you can use";
    case "cancel":
      return "Operation cancelled";
    case "unknown":
    default:
      return `I didn't understand that command. Please try again.`;
  }
};

/**
 * Get available commands for help
 * @returns {Array} - List of available commands and their descriptions
 */
export const getAvailableCommands = () => {
  return [
    { command: "Search for [product]", description: "Search for products" },
    { command: "Show me [product]", description: "Search for products" },
    {
      command: "Select item [number]",
      description: "Select a product from the list",
    },
    {
      command: "Add to cart",
      description: "Add the current product to your cart",
    },
    { command: "View cart", description: "Open your shopping cart" },
    {
      command: "Remove from cart",
      description: "Remove an item from your cart",
    },
    { command: "Clear cart", description: "Remove all items from your cart" },
    {
      command: "Update quantity to [number]",
      description: "Change the quantity of an item",
    },
    { command: "Checkout", description: "Proceed to checkout" },
    { command: "Confirm payment", description: "Complete your purchase" },
    { command: "Go back", description: "Return to the previous screen" },
    { command: "Go home", description: "Return to the home screen" },
    { command: "Help", description: "Show available commands" },
    { command: "Cancel", description: "Cancel the current operation" },
  ];
};

export default {
  extractSearchTerm,
  extractProductSelection,
  extractQuantity,
  determineAction,
  generateResponse,
  getAvailableCommands,
};
