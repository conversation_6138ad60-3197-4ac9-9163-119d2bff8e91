# VibeShop - Voice-Controlled Shopping App

VibeShop is a modern React Native mobile application that allows users to shop using voice commands. This guide will walk you through setting up the entire application from scratch.

## Table of Contents
1. [System Requirements](#system-requirements)
2. [Setting Up the Development Environment](#setting-up-the-development-environment)
3. [Installing the Application](#installing-the-application)
4. [Backend Setup](#backend-setup)
5. [Firebase Configuration](#firebase-configuration)
6. [Running the Application](#running-the-application)
7. [Troubleshooting](#troubleshooting)
8. [Voice Commands Guide](#voice-commands-guide)

## System Requirements

- Windows 10 or later
- Node.js 16.x or later
- Python 3.8 or later (for the backend)
- Git (optional, for version control)
- Expo Go app installed on your mobile device for testing

## Setting Up the Development Environment

### 1. Install Node.js
1. Download Node.js from [https://nodejs.org/](https://nodejs.org/) (LTS version recommended)
2. Run the installer and follow the instructions
3. Verify installation by opening PowerShell and running:
   ```
   node --version
   npm --version
   ```

### 2. Install Python
1. Download Python from [https://www.python.org/downloads/](https://www.python.org/downloads/)
2. During installation, make sure to check "Add Python to PATH"
3. Verify installation by opening PowerShell and running:
   ```
   python --version
   pip --version
   ```

### 3. Install Expo CLI
```
npm install -g expo-cli
```

## Installing the Application

### 1. Extract the ZIP File
1. Extract the VibeShop ZIP file to a location of your choice (e.g., `C:\Users\<USER>\Desktop\`)

### 2. Install Frontend Dependencies
1. Open PowerShell and navigate to the extracted folder:
   ```
   cd C:\path\to\VibeShop
   ```
2. Install the required Node.js packages:
   ```
   npm install
   ```
   This will install all dependencies listed in the package.json file, including:
   - React and React Native
   - Expo
   - React Navigation
   - Firebase
   - React Native Voice
   - React Native Paystack Webview
   - Other UI and utility libraries

## Backend Setup

The backend uses Python with Flask to handle voice recognition and processing.

### 1. Set Up Python Virtual Environment
1. Navigate to the backend directory:
   ```
   cd backend
   ```
2. Create and activate a virtual environment:
   ```
   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   .\venv\Scripts\Activate
   ```
   If you encounter execution policy errors, run PowerShell as administrator and execute:
   ```
   Set-ExecutionPolicy RemoteSigned
   ```

### 2. Install Backend Dependencies
With the virtual environment activated, install the required Python packages:
```
pip install -r requirements.txt
```

This will install:
- Flask
- PyTorch
- Transformers
- SpeechRecognition
- Flask-CORS
- Other audio processing libraries

### 3. Configure Backend IP Address

The backend needs to run on your machine's IP address so that the mobile app can connect to it.

1. Find your machine's IP address:
   ```
   ipconfig
   ```
   Look for the IPv4 Address under your active network adapter (e.g., ***********)

2. Update the API configuration in the frontend:
   Open `src/config/api.js` and update the `BASE_URL` with your IP address:
   ```javascript
   export const BASE_URL = 'http://YOUR_IP_ADDRESS:5000';
   ```

### 4. Start the Backend Server
Run the backend server using the provided batch file:
```
.\run.bat
```

Or manually with:
```
python run.py
```

The server will start on port 5000 and will be accessible at http://YOUR_IP_ADDRESS:5000

## Firebase Configuration

VibeShop uses Firebase for authentication, database, and storage.

### 1. Create a Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project" and follow the setup wizard
3. Give your project a name (e.g., "VibeShop")
4. Enable Google Analytics if desired
5. Click "Create project"

### 2. Add a Web App to Your Firebase Project
1. From the project overview page, click the web icon (</>) to add a web app
2. Register the app with a nickname (e.g., "VibeShop Web")
3. Check "Also set up Firebase Hosting" if desired
4. Click "Register app"
5. Copy the Firebase configuration object

### 3. Update Firebase Configuration in the App
1. Open `src/config/firebase.js`
2. Replace the existing configuration with your Firebase config:
   ```javascript
   const firebaseConfig = {
     apiKey: "YOUR_API_KEY",
     authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
     projectId: "YOUR_PROJECT_ID",
     storageBucket: "YOUR_PROJECT_ID.appspot.com",
     messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
     appId: "YOUR_APP_ID",
     measurementId: "YOUR_MEASUREMENT_ID"
   };
   ```

### 4. Set Up Firebase Authentication
1. In the Firebase Console, go to "Authentication" > "Sign-in method"
2. Enable Email/Password authentication
3. Optionally enable Google, Facebook, or other providers as needed

### 5. Set Up Cloud Firestore
1. Go to "Firestore Database" > "Create database"
2. Start in test mode for development purposes
3. Choose a location closest to your users
4. Create the following collections:
   - `products` - For storing product information
   - `carts` - For storing user shopping carts
   - `orders` - For storing completed orders

### 6. Set Up Firebase Storage (for product images)
1. Go to "Storage" > "Get started"
2. Start in test mode for development
3. Choose a location closest to your users

## Running the Application

### 1. Start the Backend Server
If not already running:
```
cd backend
.\venv\Scripts\Activate
python run.py
```

### 2. Start the Frontend Development Server
In a new PowerShell window:
```
cd C:\path\to\VibeShop
npm start
```

This will start the Expo development server. You'll see a QR code in the terminal.

### 3. Connect with Expo Go App
1. Install the Expo Go app on your mobile device from the App Store or Google Play Store
2. Make sure your mobile device is on the same WiFi network as your computer
3. Scan the QR code with your device's camera or the Expo Go app
4. The app should load on your device

### 4. Alternative: Run on Emulator/Simulator
If you have Android Studio or Xcode installed:
- For Android: Press 'a' in the terminal where Expo is running
- For iOS (Mac only): Press 'i' in the terminal where Expo is running

## Troubleshooting

### Connection Issues
- Ensure your mobile device and computer are on the same WiFi network
- Check if any firewall is blocking the connection
- Verify the IP address in `src/config/api.js` matches your computer's current IP address
- Try disabling Windows Firewall temporarily to test the connection

### Backend Server Issues
- Ensure all dependencies are installed: `pip install -r requirements.txt`
- Check if port 5000 is already in use by another application
- Look for error messages in the console when starting the backend

### Voice Recognition Issues
- Ensure your device has granted microphone permissions to the Expo Go app
- Speak clearly and in a quiet environment
- Check the backend console for speech recognition errors

### Firebase Issues
- Verify your Firebase configuration in `src/config/firebase.js`
- Ensure you've set up the required collections in Firestore
- Check Firebase console for any authentication or database errors

## Voice Commands Guide

See the [VOICE_COMMANDS_GUIDE.md](./VOICE_COMMANDS_GUIDE.md) file for a complete list of available voice commands.

## Additional Resources

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Expo Documentation](https://docs.expo.dev/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Flask Documentation](https://flask.palletsprojects.com/)
- [Paystack Documentation](https://paystack.com/docs/)
