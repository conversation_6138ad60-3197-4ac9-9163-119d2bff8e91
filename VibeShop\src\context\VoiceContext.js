import React, { createContext, useState, useContext, useEffect } from "react";
import { Alert } from "react-native";
import voiceService from "../services/voiceService";
import productService from "../services/productService";
import cartService from "../services/cartService";
import { getAvailableCommands } from "../utils/commandProcessor";

// Create context
const VoiceContext = createContext();

// Voice command status
const STATUS = {
  IDLE: "idle",
  LISTENING: "listening",
  PROCESSING: "processing",
  SPEAKING: "speaking",
};

// Provider component
export const VoiceProvider = ({ children }) => {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [lastCommand, setLastCommand] = useState("");
  const [processingError, setProcessingError] = useState(null);
  const [feedbackMessage, setFeedbackMessage] = useState("");
  const [currentProductId, setCurrentProductId] = useState(null);
  const [currentProductList, setCurrentProductList] = useState([]);
  const [currentCartItem, setCurrentCartItem] = useState(null);
  const [recognizedCommand, setRecognizedCommand] = useState("");
  const [showTranscript, setShowTranscript] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState("");

  // Use global navigate function if available
  const navigate = (name, params) => {
    if (global.navigate) {
      global.navigate(name, params);
    }
  };

  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      if (isListening) {
        stopListening();
      }
    };
  }, []);

  // Start listening for voice commands
  const startListening = async () => {
    try {
      setStatus(STATUS.LISTENING);
      setIsListening(true);
      setTranscript("");
      setProcessingError(null);
      setFeedbackMessage("Listening...");
      setRecognizedCommand("");

      await voiceService.startRecording();
    } catch (error) {
      console.error("Error starting voice recording:", error);
      setProcessingError(error.message);
      setStatus(STATUS.IDLE);
      setIsListening(false);
      setFeedbackMessage("Failed to start listening");
    }
  };

  // Stop listening and process the voice command
  const stopListening = async () => {
    try {
      setStatus(STATUS.PROCESSING);
      setIsListening(false);
      setFeedbackMessage("Processing...");

      const audioUri = await voiceService.stopRecording();

      if (!audioUri) {
        setStatus(STATUS.IDLE);
        setFeedbackMessage("No audio recorded");
        return;
      }

      try {
        // Process the audio
        const result = await voiceService.processAudio(audioUri);
        setTranscript(result.text);
        setLastCommand(result.text);
        setRecognizedCommand(`Recognized: "${result.text}"`);

        // Show transcript overlay
        setCurrentTranscript(result.text);
        setShowTranscript(true);

        // Process the command
        await processCommand(result.text);
      } catch (error) {
        console.error("Error processing voice command:", error);
        setProcessingError(error.message);
        setStatus(STATUS.IDLE);

        // Provide clear feedback about server issues
        if (error.message.includes("server")) {
          setFeedbackMessage(
            "Speech recognition server is not available. Please check your connection and try again."
          );
          await voiceService.speak(
            "Speech recognition server is not available. Please check your connection and try again."
          );
        } else {
          setFeedbackMessage(`Error: ${error.message}`);
          await voiceService.speak(
            "Sorry, there was an error processing your voice command. Please try again."
          );
        }
      }
    } catch (error) {
      console.error("Error processing voice command:", error);
      setProcessingError(error.message);
      setStatus(STATUS.IDLE);
      setFeedbackMessage(`Error: ${error.message}`);
    }
  };

  // Process the voice command
  const processCommand = async (command) => {
    try {
      const result = await voiceService.processCommand(command);
      console.log("Command processed:", result);

      // If the command is unknown, try to find a close match
      if (result.action === "unknown") {
        const commandLower = command.toLowerCase().trim();

        // Check for common speech recognition errors
        if (
          commandLower.includes("add to cut") ||
          commandLower.includes("add to car")
        ) {
          await handleAddToCartCommand();
          return;
        }

        if (
          commandLower.includes("view card") ||
          commandLower.includes("view court") ||
          commandLower.includes("view cart") ||
          commandLower.includes("see cart")
        ) {
          await handleViewCartCommand();
          return;
        }

        if (
          commandLower.includes("check out") ||
          commandLower.includes("checkout") ||
          commandLower.includes("proceed to check")
        ) {
          await handleCheckoutCommand();
          return;
        }

        // If still unknown, provide feedback
        setFeedbackMessage(
          `I didn't understand "${command}". Please try again.`
        );
        await voiceService.speak(
          `I didn't understand that command. Please try again.`
        );
        setStatus(STATUS.IDLE);
        return;
      }

      switch (result.action) {
        case "search":
          await handleSearchCommand(result.payload);
          break;
        case "select_product":
          await handleSelectProductCommand(result.payload);
          break;
        case "product_details":
          await handleProductDetailsCommand();
          break;
        case "add_to_cart":
          await handleAddToCartCommand();
          break;
        case "remove_from_cart":
          await handleRemoveFromCartCommand();
          break;
        case "remove_item_by_name":
          await handleRemoveItemByNameCommand(result.payload);
          break;
        case "clear_cart":
          await handleClearCartCommand();
          break;
        case "update_quantity":
          await handleUpdateQuantityCommand(result.payload);
          break;
        case "increase_quantity":
          await handleIncreaseQuantityCommand();
          break;
        case "decrease_quantity":
          await handleDecreaseQuantityCommand();
          break;
        case "increase_quantity_by_name":
          await handleIncreaseQuantityByNameCommand(result.payload);
          break;
        case "decrease_quantity_by_name":
          await handleDecreaseQuantityByNameCommand(result.payload);
          break;
        case "view_cart":
          await handleViewCartCommand();
          break;
        case "checkout":
          await handleCheckoutCommand();
          break;
        case "confirm_payment":
          await handleConfirmPaymentCommand();
          break;
        case "navigate":
          await handleNavigateCommand(result.payload);
          break;
        case "help":
          await handleHelpCommand();
          break;
        case "cancel":
          await handleCancelCommand();
          break;
        default:
          setFeedbackMessage(
            `I didn't understand "${command}". Please try again.`
          );
          await voiceService.speak(
            `I didn't understand that command. Please try again.`
          );
      }

      setStatus(STATUS.IDLE);
    } catch (error) {
      console.error("Error processing command:", error);
      setProcessingError(error.message);
      setStatus(STATUS.IDLE);
      setFeedbackMessage(`Error: ${error.message}`);
      await voiceService.speak(
        "Sorry, there was an error processing your command. Please try again."
      );
    }
  };

  // Handle search command
  const handleSearchCommand = async (searchTerm) => {
    try {
      setFeedbackMessage(`Searching for "${searchTerm}"...`);
      await voiceService.speak(`Searching for ${searchTerm}`);

      // Get search results
      const products = await productService.getMockProducts(searchTerm);

      if (products.length === 0) {
        setFeedbackMessage(`No products found for "${searchTerm}"`);
        await voiceService.speak(`No products found for ${searchTerm}`);
        return;
      }

      // Store the product list for future reference
      setCurrentProductList(products);

      setFeedbackMessage(
        `Found ${products.length} products for "${searchTerm}"`
      );
      await voiceService.speak(
        `Found ${products.length} products for ${searchTerm}. You can select a product by saying "select item" followed by a number.`
      );

      // Navigate to search results
      navigate("SearchResults", { searchTerm, products });
    } catch (error) {
      console.error("Error handling search command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error searching for products");
      await voiceService.speak(
        "Sorry, there was an error searching for products. Please try again."
      );
    }
  };

  // Handle select product command
  const handleSelectProductCommand = async (productIndex) => {
    try {
      if (!currentProductList || currentProductList.length === 0) {
        setFeedbackMessage("No products available to select");
        await voiceService.speak(
          "No products available to select. Please search for products first."
        );
        return;
      }

      let selectedProduct;

      // Check if productIndex is a number (1-based index) or a string (brand/name)
      if (typeof productIndex === "number") {
        // Adjust for 1-based indexing from voice commands
        const index = productIndex - 1;

        if (index < 0 || index >= currentProductList.length) {
          setFeedbackMessage(
            `Invalid product number. Please select between 1 and ${currentProductList.length}`
          );
          await voiceService.speak(
            `Invalid product number. Please select between 1 and ${currentProductList.length}`
          );
          return;
        }

        selectedProduct = currentProductList[index];
      } else if (typeof productIndex === "string") {
        // Search for product by name/brand
        const searchTerm = productIndex.toLowerCase();

        // Find the first product that matches the search term in name or brand
        selectedProduct = currentProductList.find(
          (product) =>
            product.name.toLowerCase().includes(searchTerm) ||
            (product.brand &&
              product.brand.toLowerCase().includes(searchTerm)) ||
            (product.category &&
              product.category.toLowerCase().includes(searchTerm)) ||
            (product.searchTerms &&
              product.searchTerms.some((term) => term.includes(searchTerm)))
        );

        if (!selectedProduct) {
          setFeedbackMessage(`No product found matching "${productIndex}"`);
          await voiceService.speak(
            `I couldn't find a product matching ${productIndex}. Please try selecting a different product.`
          );
          return;
        }
      } else {
        setFeedbackMessage("Invalid product selection");
        await voiceService.speak(
          "Invalid product selection. Please try again."
        );
        return;
      }

      setCurrentProductId(selectedProduct.id);

      setFeedbackMessage(`Selected ${selectedProduct.name}`);
      await voiceService.speak(
        `Selected ${selectedProduct.name}. You can say "add to cart" to purchase this item.`
      );

      // Navigate to product details
      navigate("ProductDetail", { product: selectedProduct });
    } catch (error) {
      console.error("Error handling select product command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error selecting product");
      await voiceService.speak(
        "Sorry, there was an error selecting the product. Please try again."
      );
    }
  };

  // Handle product details command
  const handleProductDetailsCommand = async () => {
    try {
      if (!currentProductId) {
        setFeedbackMessage("No product selected");
        await voiceService.speak(
          "No product is currently selected. Please select a product first."
        );
        return;
      }

      const product = await productService.getProductById(currentProductId);

      if (!product) {
        setFeedbackMessage("Product not found");
        await voiceService.speak(
          "Sorry, the product information is not available."
        );
        return;
      }

      setFeedbackMessage(`Showing details for ${product.name}`);
      await voiceService.speak(
        `${product.name}. Price: ${product.price}. ${product.description}`
      );
    } catch (error) {
      console.error("Error handling product details command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error showing product details");
      await voiceService.speak(
        "Sorry, there was an error showing the product details. Please try again."
      );
    }
  };

  // Handle add to cart command
  const handleAddToCartCommand = async () => {
    try {
      if (!currentProductId) {
        setFeedbackMessage("No product selected to add to cart");
        await voiceService.speak(
          "Please select a product first to add it to your cart."
        );
        return;
      }

      const product = await productService.getProductById(currentProductId);

      if (!product) {
        setFeedbackMessage("Product not found");
        await voiceService.speak(
          "Sorry, the product information is not available."
        );
        return;
      }

      // Add to cart
      await cartService.addItem(product);
      setCurrentCartItem(product);

      setFeedbackMessage(`Added ${product.name} to your cart`);
      await voiceService.speak(
        `Added ${product.name} to your cart. You can say "view cart" to see your cart or "checkout" to proceed to payment.`
      );
    } catch (error) {
      console.error("Error handling add to cart command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error adding item to cart");
      await voiceService.speak(
        "Sorry, there was an error adding the item to your cart. Please try again."
      );
    }
  };

  // Handle remove from cart command
  const handleRemoveFromCartCommand = async () => {
    try {
      if (!currentCartItem) {
        setFeedbackMessage("No item selected to remove");
        await voiceService.speak("Please select an item from your cart first.");
        return;
      }

      // Remove from cart
      await cartService.removeItem(currentCartItem.id);

      setFeedbackMessage(`Removed ${currentCartItem.name} from your cart`);
      await voiceService.speak(
        `Removed ${currentCartItem.name} from your cart.`
      );

      setCurrentCartItem(null);
    } catch (error) {
      console.error("Error handling remove from cart command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error removing item from cart");
    }
  };

  // Handle clear cart command
  const handleClearCartCommand = async () => {
    try {
      // Clear cart
      await cartService.clearCart();

      setFeedbackMessage("Cart cleared");
      await voiceService.speak("Your cart has been cleared.");

      setCurrentCartItem(null);
    } catch (error) {
      console.error("Error handling clear cart command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error clearing cart");
    }
  };

  // Handle update quantity command
  const handleUpdateQuantityCommand = async (quantity) => {
    try {
      if (!currentCartItem) {
        setFeedbackMessage("No item selected to update");
        await voiceService.speak("Please select an item from your cart first.");
        return;
      }

      if (typeof quantity !== "number" || quantity < 1) {
        setFeedbackMessage("Invalid quantity");
        await voiceService.speak("Please specify a valid quantity.");
        return;
      }

      // Update quantity
      await cartService.updateItemQuantity(currentCartItem.id, quantity);

      setFeedbackMessage(
        `Updated ${currentCartItem.name} quantity to ${quantity}`
      );
      await voiceService.speak(
        `Updated ${currentCartItem.name} quantity to ${quantity}.`
      );
    } catch (error) {
      console.error("Error handling update quantity command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error updating quantity");
    }
  };

  // Handle remove item by name command
  const handleRemoveItemByNameCommand = async (itemName) => {
    try {
      if (!itemName) {
        setFeedbackMessage("Please specify which item to remove");
        await voiceService.speak(
          "Please specify which item you want to remove from your cart."
        );
        return;
      }

      // Get current cart
      const cart = await cartService.getCart();

      // Find item by name (case-insensitive partial match)
      const itemToRemove = cart.items.find((item) =>
        item.name.toLowerCase().includes(itemName.toLowerCase().trim())
      );

      if (!itemToRemove) {
        setFeedbackMessage(`Item "${itemName}" not found in cart`);
        await voiceService.speak(`I couldn't find "${itemName}" in your cart.`);
        return;
      }

      // Remove the item
      await cartService.removeItem(itemToRemove.id);

      setFeedbackMessage(`Removed ${itemToRemove.name} from your cart`);
      await voiceService.speak(`Removed ${itemToRemove.name} from your cart.`);
    } catch (error) {
      console.error("Error handling remove item by name command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error removing item from cart");
    }
  };

  // Handle increase quantity command
  const handleIncreaseQuantityCommand = async () => {
    try {
      if (!currentCartItem) {
        setFeedbackMessage("No item selected to increase");
        await voiceService.speak("Please select an item from your cart first.");
        return;
      }

      // Increase quantity by 1
      const newQuantity = currentCartItem.quantity + 1;
      await cartService.updateItemQuantity(currentCartItem.id, newQuantity);

      setFeedbackMessage(
        `Increased ${currentCartItem.name} quantity to ${newQuantity}`
      );
      await voiceService.speak(
        `Increased ${currentCartItem.name} quantity to ${newQuantity}.`
      );
    } catch (error) {
      console.error("Error handling increase quantity command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error increasing quantity");
    }
  };

  // Handle decrease quantity command
  const handleDecreaseQuantityCommand = async () => {
    try {
      if (!currentCartItem) {
        setFeedbackMessage("No item selected to decrease");
        await voiceService.speak("Please select an item from your cart first.");
        return;
      }

      // Decrease quantity by 1, but don't go below 1
      const newQuantity = Math.max(1, currentCartItem.quantity - 1);

      if (newQuantity === 1 && currentCartItem.quantity === 1) {
        // If already at 1, ask if they want to remove it
        setFeedbackMessage(
          `${currentCartItem.name} is already at minimum quantity. Say "remove" to remove it from cart.`
        );
        await voiceService.speak(
          `${currentCartItem.name} is already at minimum quantity. Say "remove" to remove it from your cart.`
        );
        return;
      }

      await cartService.updateItemQuantity(currentCartItem.id, newQuantity);

      setFeedbackMessage(
        `Decreased ${currentCartItem.name} quantity to ${newQuantity}`
      );
      await voiceService.speak(
        `Decreased ${currentCartItem.name} quantity to ${newQuantity}.`
      );
    } catch (error) {
      console.error("Error handling decrease quantity command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error decreasing quantity");
    }
  };

  // Handle increase quantity by name command
  const handleIncreaseQuantityByNameCommand = async (itemName) => {
    try {
      if (!itemName) {
        setFeedbackMessage("Please specify which item to increase");
        await voiceService.speak(
          "Please specify which item you want to increase the quantity for."
        );
        return;
      }

      // Get current cart
      const cart = await cartService.getCart();

      // Find item by name (case-insensitive partial match)
      const itemToUpdate = cart.items.find((item) =>
        item.name.toLowerCase().includes(itemName.toLowerCase().trim())
      );

      if (!itemToUpdate) {
        setFeedbackMessage(`Item "${itemName}" not found in cart`);
        await voiceService.speak(`I couldn't find "${itemName}" in your cart.`);
        return;
      }

      // Increase quantity by 1
      const newQuantity = itemToUpdate.quantity + 1;
      await cartService.updateItemQuantity(itemToUpdate.id, newQuantity);

      setFeedbackMessage(
        `Increased ${itemToUpdate.name} quantity to ${newQuantity}`
      );
      await voiceService.speak(
        `Increased ${itemToUpdate.name} quantity to ${newQuantity}.`
      );
    } catch (error) {
      console.error("Error handling increase quantity by name command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error increasing item quantity");
    }
  };

  // Handle decrease quantity by name command
  const handleDecreaseQuantityByNameCommand = async (itemName) => {
    try {
      if (!itemName) {
        setFeedbackMessage("Please specify which item to decrease");
        await voiceService.speak(
          "Please specify which item you want to decrease the quantity for."
        );
        return;
      }

      // Get current cart
      const cart = await cartService.getCart();

      // Find item by name (case-insensitive partial match)
      const itemToUpdate = cart.items.find((item) =>
        item.name.toLowerCase().includes(itemName.toLowerCase().trim())
      );

      if (!itemToUpdate) {
        setFeedbackMessage(`Item "${itemName}" not found in cart`);
        await voiceService.speak(`I couldn't find "${itemName}" in your cart.`);
        return;
      }

      // Decrease quantity by 1, but don't go below 1
      const newQuantity = Math.max(1, itemToUpdate.quantity - 1);

      if (newQuantity === 1 && itemToUpdate.quantity === 1) {
        // If already at 1, remove the item
        await cartService.removeItem(itemToUpdate.id);
        setFeedbackMessage(`Removed ${itemToUpdate.name} from your cart`);
        await voiceService.speak(
          `Removed ${itemToUpdate.name} from your cart since it was at minimum quantity.`
        );
        return;
      }

      await cartService.updateItemQuantity(itemToUpdate.id, newQuantity);

      setFeedbackMessage(
        `Decreased ${itemToUpdate.name} quantity to ${newQuantity}`
      );
      await voiceService.speak(
        `Decreased ${itemToUpdate.name} quantity to ${newQuantity}.`
      );
    } catch (error) {
      console.error("Error handling decrease quantity by name command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error decreasing item quantity");
    }
  };

  // Handle view cart command
  const handleViewCartCommand = async () => {
    try {
      setFeedbackMessage("Opening your cart");
      await voiceService.speak("Opening your cart.");

      // Get cart items for feedback
      const cart = await cartService.getCart();

      if (cart.items.length === 0) {
        await voiceService.speak(
          "Your cart is empty. You can search for products to add to your cart."
        );
      } else {
        await voiceService.speak(
          `Your cart has ${cart.items.length} items. You can say "checkout" to proceed to payment.`
        );
      }

      navigate("Cart");
    } catch (error) {
      console.error("Error handling view cart command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error viewing cart");
    }
  };

  // Handle checkout command
  const handleCheckoutCommand = async () => {
    try {
      // Check if cart has items
      const cart = await cartService.getCart();

      if (cart.items.length === 0) {
        setFeedbackMessage("Your cart is empty");
        await voiceService.speak(
          "Your cart is empty. Please add items to your cart before checking out."
        );
        return;
      }

      setFeedbackMessage("Proceeding to checkout");
      await voiceService.speak(
        'Proceeding to checkout. You can say "confirm payment" to complete your order.'
      );

      navigate("Checkout");
    } catch (error) {
      console.error("Error handling checkout command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error proceeding to checkout");
    }
  };

  // Handle confirm payment command
  const handleConfirmPaymentCommand = async () => {
    try {
      setFeedbackMessage("Please complete payment through the payment screen");
      await voiceService.speak(
        "Please complete your payment using the payment form or Paystack gateway. Voice commands cannot process actual payments for security reasons."
      );
    } catch (error) {
      console.error("Error handling confirm payment command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error processing payment command");
    }
  };

  // Handle navigate command
  const handleNavigateCommand = async (destination) => {
    try {
      switch (destination) {
        case "home":
          setFeedbackMessage("Going to home page");
          await voiceService.speak("Going to home page.");
          navigate("Home");
          break;
        default:
          setFeedbackMessage("Going back");
          await voiceService.speak("Going back.");
          // Use the navigation goBack function if available
          if (global.goBack) {
            global.goBack();
          }
      }
    } catch (error) {
      console.error("Error handling navigate command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error navigating");
    }
  };

  // Handle help command
  const handleHelpCommand = async () => {
    try {
      setFeedbackMessage("Here are the available commands");

      // Get available commands
      const commands = getAvailableCommands();

      // Speak the available commands
      await voiceService.speak("Here are some commands you can use:");

      // Speak a few key commands (not all to avoid too much speech)
      const keyCommands = [
        'Search for products by saying "search for" followed by what you want.',
        'Select a product by saying "select item" followed by a number.',
        'Add a product to your cart by saying "add to cart".',
        'View your cart by saying "view cart".',
        'Checkout by saying "checkout".',
        'Confirm your payment by saying "confirm payment".',
      ];

      for (const command of keyCommands) {
        await voiceService.speak(command);
      }

      // Display all commands in the feedback message
      setFeedbackMessage(
        "Available commands: " + commands.map((cmd) => cmd.command).join(", ")
      );
    } catch (error) {
      console.error("Error handling help command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error displaying help");
    }
  };

  // Handle cancel command
  const handleCancelCommand = async () => {
    try {
      setFeedbackMessage("Operation cancelled");
      await voiceService.speak("Operation cancelled.");
    } catch (error) {
      console.error("Error handling cancel command:", error);
      setProcessingError(error.message);
      setFeedbackMessage("Error cancelling operation");
    }
  };

  // Speak feedback message
  const speakFeedback = async (message) => {
    try {
      setStatus(STATUS.SPEAKING);
      await voiceService.speak(message);
      setStatus(STATUS.IDLE);
    } catch (error) {
      console.error("Error speaking feedback:", error);
      setStatus(STATUS.IDLE);
    }
  };

  // Close transcript overlay
  const closeTranscript = () => {
    setShowTranscript(false);
    setCurrentTranscript("");
  };

  // Context value
  const value = {
    status,
    isListening,
    transcript,
    lastCommand,
    processingError,
    feedbackMessage,
    startListening,
    stopListening,
    speakFeedback,
    STATUS,
    navigate,
    currentProductId,
    currentProductList,
    currentCartItem,
    recognizedCommand,
    showTranscript,
    currentTranscript,
    closeTranscript,
  };

  return (
    <VoiceContext.Provider value={value}>{children}</VoiceContext.Provider>
  );
};

// Custom hook for using voice context
export const useVoice = () => {
  const context = useContext(VoiceContext);
  if (!context) {
    throw new Error("useVoice must be used within a VoiceProvider");
  }
  return context;
};

export default VoiceContext;
